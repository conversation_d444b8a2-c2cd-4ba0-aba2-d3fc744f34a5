# Testing Dynamic Usage Enforcement

## Setup for Testing

### 1. Disable Trial (Already Done)
The 7-day free trial auto-activation has been disabled. New users will start on the free tier immediately.

### 2. Force Expire Existing Trial (If Needed)
If you have an active trial, run this in the browser console on any page with the extension:

```javascript
// Force expire trial to test free tier limits
if (typeof window.StashyPremium !== 'undefined') {
    window.StashyPremium.forceExpireTrial().then(() => {
        console.log('Trial expired - now testing free tier');
        location.reload(); // Reload to update UI
    });
} else {
    console.log('Premium manager not loaded yet');
}
```

## Testing the Dynamic Usage Enforcement

### Test 1: Note Creation Limits (Max 10)

1. **Open any webpage** with the extension active
2. **Create notes** by typing in the note area (this triggers auto-save)
3. **Monitor console** for enforcement messages
4. **Expected behavior**: After 10 notes, creation should be blocked

**Console Test Script:**
```javascript
// Check current note count
if (typeof window.StashyPremium !== 'undefined') {
    window.StashyPremium.countActualItems('notes').then(count => {
        console.log(`Current notes: ${count}/10`);
        
        // Test if can create more
        window.StashyPremium.canCreateItem('note').then(result => {
            console.log('Can create note:', result);
            if (!result.canCreate) {
                console.log('ENFORCEMENT WORKING:', result.message);
            }
        });
    });
}
```

### Test 2: Notebook Creation Limits (Max 2)

1. **Open dashboard** (click extension icon → "Open Dashboard")
2. **Click "Add Notebook"** button
3. **Create 2 notebooks**
4. **Try to create a 3rd** - should be blocked

**Console Test Script:**
```javascript
// Check current notebook count
if (typeof window.StashyPremium !== 'undefined') {
    window.StashyPremium.countActualItems('notebooks').then(count => {
        console.log(`Current notebooks: ${count}/2`);
        
        // Test if can create more
        window.StashyPremium.canCreateItem('notebook').then(result => {
            console.log('Can create notebook:', result);
            if (!result.canCreate) {
                console.log('ENFORCEMENT WORKING:', result.message);
            }
        });
    });
}
```

### Test 3: Highlight Creation Limits (Max 10)

1. **Select text** on any webpage
2. **Right-click** and choose highlight option
3. **Create 10 highlights**
4. **Try to create an 11th** - should be blocked

**Console Test Script:**
```javascript
// Check current highlight count
if (typeof window.StashyPremium !== 'undefined') {
    window.StashyPremium.countActualItems('highlights').then(count => {
        console.log(`Current highlights: ${count}/10`);
        
        // Test if can create more
        window.StashyPremium.canCreateItem('highlight').then(result => {
            console.log('Can create highlight:', result);
            if (!result.canCreate) {
                console.log('ENFORCEMENT WORKING:', result.message);
            }
        });
    });
}
```

## Expected Enforcement Messages

When limits are reached, you should see:

### Notes (10/10):
```
"You have 10/10 notes. Delete some notes to create new ones, or upgrade to Stashy Pro for unlimited notes."
```

### Notebooks (2/2):
```
"You have 2/2 notebooks. Delete some notebooks to create new ones, or upgrade to Stashy Pro for unlimited notebooks."
```

### Highlights (10/10):
```
"You have 10/10 highlights. Delete some highlights to create new ones, or upgrade to Stashy Pro for unlimited highlights."
```

## Visual Indicators to Look For

### Dashboard:
- **Usage counters** showing current/limit (e.g., "10/10")
- **Red warning colors** when at limit
- **Enforcement banners** when trying to create items at limit

### Popup:
- **Usage displays** with warning colors
- **At-limit styling** with red backgrounds

### Content Scripts:
- **Blocked creation** with clear messages
- **Upgrade prompts** alongside enforcement messages

## Complete Test Script

Run this comprehensive test in the browser console:

```javascript
async function testDynamicEnforcement() {
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded');
        return;
    }
    
    console.log('🧪 Testing Dynamic Usage Enforcement...');
    
    // Check premium status
    const isPremium = window.StashyPremium.isPremium();
    console.log('Premium status:', isPremium ? 'PREMIUM' : 'FREE TIER');
    
    if (isPremium) {
        console.log('⚠️ User is premium - enforcement won\'t trigger');
        return;
    }
    
    // Test all item types
    const types = ['notes', 'notebooks', 'highlights'];
    const limits = [10, 2, 10];
    
    for (let i = 0; i < types.length; i++) {
        const type = types[i];
        const limit = limits[i];
        
        console.log(`\n📊 Testing ${type}:`);
        
        const count = await window.StashyPremium.countActualItems(type);
        console.log(`  Current count: ${count}/${limit}`);
        
        const canCreate = await window.StashyPremium.canCreateItem(type);
        console.log(`  Can create: ${canCreate.canCreate}`);
        
        if (!canCreate.canCreate) {
            console.log(`  ✅ ENFORCEMENT ACTIVE: ${canCreate.message}`);
        } else {
            console.log(`  ⚠️ Can still create ${limit - count} more`);
        }
    }
    
    console.log('\n🎯 Test complete!');
}

// Run the test
testDynamicEnforcement();
```

## Troubleshooting

### If enforcement isn't working:
1. **Check console** for error messages
2. **Verify free tier status** (not premium/trial)
3. **Reload the page** to ensure scripts are loaded
4. **Check storage** for existing items

### If functions are missing:
1. **Wait a few seconds** for scripts to load
2. **Try on different pages** (some sites may block scripts)
3. **Check extension permissions** are granted

### Manual Reset (if needed):
```javascript
// Clear all data to start fresh (CAUTION: This deletes everything)
chrome.storage.local.clear().then(() => {
    console.log('Storage cleared - reload extension');
    location.reload();
});
```

## Success Criteria

✅ **Dynamic enforcement is working if:**
- Creation is blocked when limits are reached
- Clear messages explain the situation
- Upgrade prompts are shown
- Visual indicators appear in UI
- Console shows enforcement logs

The system should provide a smooth user experience that guides users toward upgrading while clearly explaining the limitations.
