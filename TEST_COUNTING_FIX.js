// Test script to verify the counting fix is working
// Run this in browser console after reloading the extension

async function testCountingFix() {
    console.log('🧪 Testing Dynamic Usage Enforcement Counting Fix...');
    
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded - wait a moment and try again');
        return;
    }
    
    console.log('✅ Premium manager loaded');
    
    // Test both singular and plural forms
    const testTypes = [
        'note', 'notes',
        'notebook', 'notebooks', 
        'highlight', 'highlights'
    ];
    
    console.log('\n📊 Testing counting with different type formats:');
    
    for (const type of testTypes) {
        try {
            const count = await window.StashyPremium.countActualItems(type);
            console.log(`✅ ${type}: ${count} items`);
        } catch (error) {
            console.log(`❌ ${type}: Error - ${error.message}`);
        }
    }
    
    console.log('\n🎯 Testing canCreateItem function:');
    
    const createTestTypes = ['note', 'notebook', 'highlight'];
    
    for (const type of createTestTypes) {
        try {
            const result = await window.StashyPremium.canCreateItem(type);
            console.log(`✅ ${type}:`, {
                canCreate: result.canCreate,
                currentCount: result.currentCount,
                limit: result.limit,
                message: result.message || 'No message'
            });
        } catch (error) {
            console.log(`❌ ${type}: Error - ${error.message}`);
        }
    }
    
    // Check premium status
    console.log('\n👑 Premium Status:');
    const isPremium = window.StashyPremium.isPremium();
    console.log('Is Premium:', isPremium ? 'YES (unlimited)' : 'NO (free tier limits)');
    
    if (!isPremium) {
        console.log('\n🎯 Ready to test enforcement!');
        console.log('Try creating items to test the limits:');
        console.log('- Notes: Create content in note area');
        console.log('- Notebooks: Click "Add Notebook" in dashboard');
        console.log('- Highlights: Select text and highlight it');
    }
    
    console.log('\n✅ Test completed!');
}

// Auto-run the test
console.log('🚀 Running counting fix test...');
testCountingFix();
