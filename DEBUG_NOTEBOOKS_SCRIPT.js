// Debug script specifically for notebook enforcement
// Run this in browser console on the dashboard page

async function debugNotebookEnforcement() {
    console.log('📚 DEBUG: Checking notebook enforcement...');
    
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded');
        return;
    }
    
    // Check premium status
    const isPremium = window.StashyPremium.isPremium();
    console.log('👑 Premium status:', isPremium ? 'PREMIUM' : 'FREE TIER');
    
    if (isPremium) {
        console.log('⚠️ User is premium - enforcement won\'t trigger');
        return;
    }
    
    console.log('\n📊 Checking notebook storage...');
    
    // Check all possible storage keys for notebooks
    const allStorage = await chrome.storage.local.get(null);
    
    console.log('🔍 Looking for notebook storage keys...');
    const notebookKeys = Object.keys(allStorage).filter(key => 
        key.toLowerCase().includes('notebook')
    );
    
    console.log('Found notebook-related keys:', notebookKeys);
    
    // Check specific keys
    const stashyNotebooks = allStorage['stashy_notebooks'];
    const StashyNotebooks = allStorage['Stashy_notebooks'];
    
    console.log('📝 Notebook data:');
    console.log('  stashy_notebooks:', stashyNotebooks);
    console.log('  Stashy_notebooks:', StashyNotebooks);
    
    // Test counting function
    console.log('\n🧮 Testing notebook counting...');
    const countedNotebooks = await window.StashyPremium.countActualItems('notebooks');
    console.log(`Counted notebooks: ${countedNotebooks}`);
    
    // Test canCreateItem
    console.log('\n🎯 Testing canCreateItem for notebooks...');
    const canCreate = await window.StashyPremium.canCreateItem('notebook');
    console.log('Can create notebook:', canCreate);
    
    // Check if we should be at limit
    if (countedNotebooks >= 2) {
        console.log('\n⚠️ Should be at limit (2/2) - testing enforcement...');
        if (canCreate.canCreate) {
            console.log('❌ PROBLEM: canCreate is true but should be false!');
            console.log('🔧 Debugging why enforcement isn\'t working...');
            
            // Check the actual notebook data
            const notebooks = stashyNotebooks || StashyNotebooks || [];
            console.log('Raw notebook array:', notebooks);
            console.log('Array length:', notebooks.length);
            console.log('Is array?', Array.isArray(notebooks));
            
            if (Array.isArray(notebooks)) {
                notebooks.forEach((notebook, index) => {
                    console.log(`  Notebook ${index}:`, {
                        name: notebook?.name,
                        type: typeof notebook,
                        valid: notebook && typeof notebook === 'object' && notebook.name && notebook.name.trim() !== ''
                    });
                });
            }
            
        } else {
            console.log('✅ Enforcement working correctly!');
        }
    } else {
        console.log(`\n📝 Currently have ${countedNotebooks}/2 notebooks - create ${2 - countedNotebooks} more to test enforcement`);
    }
    
    // Test manual notebook creation
    console.log('\n🧪 Testing manual notebook creation...');
    if (typeof createNotebook === 'function') {
        console.log('✅ createNotebook function is available');
        console.log('💡 Try clicking "Add Notebook" button to test enforcement');
    } else {
        console.log('❌ createNotebook function not available (might be in different scope)');
    }
    
    // Show current dashboard notebooks
    console.log('\n📋 Current dashboard notebooks:');
    if (typeof notebooks !== 'undefined') {
        console.log('Dashboard notebooks variable:', notebooks);
    } else {
        console.log('❌ Dashboard notebooks variable not available');
    }
}

// Function to manually test notebook creation
async function testNotebookCreation() {
    console.log('🧪 Testing notebook creation manually...');
    
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded');
        return;
    }
    
    const canCreate = await window.StashyPremium.canCreateItem('notebook');
    console.log('Can create notebook:', canCreate);
    
    if (!canCreate.canCreate) {
        console.log('🚫 Notebook creation blocked:', canCreate.message);
        window.StashyPremium.showUsageEnforcementMessage(canCreate, 'manual-test');
    } else {
        console.log('✅ Notebook creation allowed');
        
        // Try to create a test notebook
        const testName = 'Test Notebook ' + Date.now();
        console.log(`Creating test notebook: ${testName}`);
        
        if (typeof createNotebook === 'function') {
            const result = await createNotebook(testName);
            console.log('Creation result:', result);
        } else {
            console.log('❌ createNotebook function not accessible');
        }
    }
}

// Function to show current notebook count in UI
function showNotebookCountInUI() {
    const addButton = document.querySelector('#add-notebook-btn');
    if (addButton) {
        // Add count display next to button
        let countDisplay = document.querySelector('#notebook-count-display');
        if (!countDisplay) {
            countDisplay = document.createElement('span');
            countDisplay.id = 'notebook-count-display';
            countDisplay.style.marginLeft = '10px';
            countDisplay.style.fontSize = '12px';
            countDisplay.style.color = '#666';
            addButton.parentNode.insertBefore(countDisplay, addButton.nextSibling);
        }
        
        if (typeof window.StashyPremium !== 'undefined') {
            window.StashyPremium.countActualItems('notebooks').then(count => {
                countDisplay.textContent = `(${count}/2)`;
                if (count >= 2) {
                    countDisplay.style.color = '#dc2626';
                    countDisplay.style.fontWeight = 'bold';
                }
            });
        }
    }
}

console.log('📚 Notebook Debug Script Loaded!');
console.log('📋 Available functions:');
console.log('- debugNotebookEnforcement() - Full notebook debug');
console.log('- testNotebookCreation() - Test notebook creation');
console.log('- showNotebookCountInUI() - Show count next to Add button');
console.log('');
console.log('🎯 Run: debugNotebookEnforcement()');

// Auto-run the debug
debugNotebookEnforcement();

// Also show count in UI
showNotebookCountInUI();
