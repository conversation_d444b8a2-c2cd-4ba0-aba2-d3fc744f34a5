// Debug script to check counting and enforcement
// Run this in browser console to debug the issue

async function debugCountingAndEnforcement() {
    console.log('🔍 DEBUG: Checking counting and enforcement...');
    
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded');
        return;
    }
    
    // Check premium status
    const isPremium = window.StashyPremium.isPremium();
    console.log('👑 Premium status:', isPremium ? 'PREMIUM' : 'FREE TIER');
    
    if (isPremium) {
        console.log('⚠️ User is premium - enforcement won\'t trigger');
        return;
    }
    
    console.log('\n📊 Checking storage keys...');
    
    // Get all storage keys
    const allStorage = await chrome.storage.local.get(null);
    const allKeys = Object.keys(allStorage);
    
    // Filter note keys
    const noteKeys = allKeys.filter(key => 
        key.startsWith('Stashy_note_') || key.startsWith('Stashy_global_note_')
    );
    
    console.log('📝 Found note keys:', noteKeys);
    
    // Check each note
    let validNotes = 0;
    for (const key of noteKeys) {
        const noteData = allStorage[key];
        const hasContent = (noteData.text && noteData.text.trim() !== '') || 
                          (noteData.title && noteData.title.trim() !== '') ||
                          (noteData.content && noteData.content.trim() !== '');
        
        console.log(`  ${key}:`, {
            hasContent,
            text: noteData.text ? noteData.text.substring(0, 50) + '...' : 'empty',
            title: noteData.title || 'no title',
            content: noteData.content ? noteData.content.substring(0, 50) + '...' : 'no content'
        });
        
        if (hasContent) validNotes++;
    }
    
    console.log(`📊 Valid notes found: ${validNotes}`);
    
    // Test counting function
    console.log('\n🧮 Testing counting function...');
    const countedNotes = await window.StashyPremium.countActualItems('notes');
    console.log(`Counted notes: ${countedNotes}`);
    
    // Test canCreateItem
    console.log('\n🎯 Testing canCreateItem...');
    const canCreate = await window.StashyPremium.canCreateItem('note');
    console.log('Can create note:', canCreate);
    
    // Check usage counters
    console.log('\n📈 Checking usage counters...');
    const usageStats = window.StashyPremium.getUsageStats();
    console.log('Usage stats:', usageStats);
    
    // Test enforcement
    if (validNotes >= 10) {
        console.log('\n⚠️ Should be at limit - testing enforcement...');
        if (canCreate.canCreate) {
            console.log('❌ PROBLEM: canCreate is true but should be false!');
        } else {
            console.log('✅ Enforcement working correctly');
        }
    } else {
        console.log(`\n📝 Currently have ${validNotes}/10 notes - create ${10 - validNotes} more to test enforcement`);
    }
    
    // Show recommendations
    console.log('\n💡 Recommendations:');
    if (validNotes < 10) {
        console.log(`- Create ${10 - validNotes} more notes with content to test enforcement`);
        console.log('- Type some text in the note area and wait for auto-save');
    } else {
        console.log('- Try typing in the note area - should be blocked');
        console.log('- Check for warning messages');
    }
}

// Also create a function to manually test note creation
async function testNoteCreation() {
    console.log('🧪 Testing note creation...');
    
    if (typeof window.StashyPremium === 'undefined') {
        console.log('❌ Premium manager not loaded');
        return;
    }
    
    const canCreate = await window.StashyPremium.canCreateItem('note');
    console.log('Can create note:', canCreate);
    
    if (!canCreate.canCreate) {
        console.log('🚫 Note creation blocked:', canCreate.message);
        window.StashyPremium.showUsageEnforcementMessage(canCreate, 'manual-test');
    } else {
        console.log('✅ Note creation allowed');
    }
}

// Function to clear some notes for testing
async function clearSomeNotes() {
    const confirm = window.confirm('Clear some notes to test enforcement? This will delete notes!');
    if (!confirm) return;
    
    console.log('🗑️ Clearing some notes...');
    
    const allStorage = await chrome.storage.local.get(null);
    const noteKeys = Object.keys(allStorage).filter(key => 
        key.startsWith('Stashy_note_') || key.startsWith('Stashy_global_note_')
    );
    
    // Remove half of the notes
    const keysToRemove = noteKeys.slice(0, Math.ceil(noteKeys.length / 2));
    
    if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`Removed ${keysToRemove.length} notes:`, keysToRemove);
        console.log('🔄 Reload the page to see updated counts');
    }
}

console.log('🚀 Debug script loaded!');
console.log('📋 Available functions:');
console.log('- debugCountingAndEnforcement() - Full debug check');
console.log('- testNoteCreation() - Test note creation');
console.log('- clearSomeNotes() - Clear some notes for testing');
console.log('');
console.log('🎯 Run: debugCountingAndEnforcement()');

// Auto-run the debug
debugCountingAndEnforcement();
