// CLEAR TRIAL SCRIPT - Run this in browser console to force free tier testing

async function clearTrialAndReload() {
    console.log('🧹 Clearing trial data for testing...');
    
    try {
        // Method 1: Use the premium manager function if available
        if (typeof window.StashyPremium !== 'undefined' && window.StashyPremium.clearTrialForTesting) {
            console.log('Using premium manager to clear trial...');
            await window.StashyPremium.clearTrialForTesting();
        }
        
        // Method 2: Direct storage clearing (backup method)
        console.log('Clearing trial storage directly...');
        await chrome.storage.local.remove([
            'stashy_trial_status',
            'stashy_install_timestamp'
        ]);
        
        // Method 3: Clear all trial-related alarms
        try {
            await chrome.alarms.clear('trialExpiry');
            await chrome.alarms.clear('trialNotification');
            console.log('Trial alarms cleared');
        } catch (error) {
            console.log('No trial alarms to clear');
        }
        
        console.log('✅ Trial data cleared successfully!');
        console.log('🔄 Reloading page to apply changes...');
        
        // Reload the page to reinitialize the extension
        setTimeout(() => {
            location.reload();
        }, 1000);
        
    } catch (error) {
        console.error('❌ Error clearing trial data:', error);
        
        // Fallback: Manual storage clear
        console.log('🔧 Trying manual storage clear...');
        try {
            const keys = await chrome.storage.local.get(null);
            const trialKeys = Object.keys(keys).filter(key => 
                key.includes('trial') || key.includes('install_timestamp')
            );
            
            if (trialKeys.length > 0) {
                await chrome.storage.local.remove(trialKeys);
                console.log('Removed trial keys:', trialKeys);
            }
            
            console.log('✅ Manual clear completed - reloading...');
            setTimeout(() => location.reload(), 1000);
            
        } catch (fallbackError) {
            console.error('❌ Fallback clear also failed:', fallbackError);
            console.log('💡 Try reloading the extension manually in chrome://extensions/');
        }
    }
}

// Alternative: Complete storage reset (CAUTION: This deletes ALL extension data)
async function completeStorageReset() {
    const confirm = window.confirm(
        'WARNING: This will delete ALL Stashy data (notes, highlights, notebooks, settings). ' +
        'Are you sure you want to proceed?'
    );
    
    if (confirm) {
        console.log('🗑️ Performing complete storage reset...');
        await chrome.storage.local.clear();
        console.log('✅ All data cleared - reloading...');
        setTimeout(() => location.reload(), 1000);
    } else {
        console.log('❌ Storage reset cancelled');
    }
}

// Quick status check
async function checkCurrentStatus() {
    console.log('📊 Current Status Check:');
    
    try {
        // Check storage
        const storage = await chrome.storage.local.get([
            'stashy_trial_status',
            'stashy_premium_status',
            'stashy_install_timestamp'
        ]);
        
        console.log('Storage data:', storage);
        
        // Check premium manager if available
        if (typeof window.StashyPremium !== 'undefined') {
            const isPremium = window.StashyPremium.isPremium();
            const trialStatus = window.StashyPremium.getTrialStatus();
            
            console.log('Premium Manager Status:');
            console.log('- Is Premium:', isPremium);
            console.log('- Trial Status:', trialStatus);
            
            // Check item counts
            const noteCount = await window.StashyPremium.countActualItems('notes');
            const notebookCount = await window.StashyPremium.countActualItems('notebooks');
            const highlightCount = await window.StashyPremium.countActualItems('highlights');
            
            console.log('Current Item Counts:');
            console.log(`- Notes: ${noteCount}/10`);
            console.log(`- Notebooks: ${notebookCount}/2`);
            console.log(`- Highlights: ${highlightCount}/10`);
            
        } else {
            console.log('❌ Premium manager not available');
        }
        
    } catch (error) {
        console.error('Error checking status:', error);
    }
}

// Main execution
console.log('🎯 Stashy Trial Clearing Script Loaded');
console.log('📋 Available functions:');
console.log('- clearTrialAndReload() - Clear trial data and reload');
console.log('- completeStorageReset() - Clear ALL data (with confirmation)');
console.log('- checkCurrentStatus() - Check current premium/trial status');
console.log('');
console.log('🚀 Run: clearTrialAndReload()');

// Auto-run the clear function
clearTrialAndReload();
