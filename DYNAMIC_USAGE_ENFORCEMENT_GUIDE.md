# Dynamic Usage Enforcement System

This document explains the new dynamic usage enforcement system implemented for free tier users in the Stashy Chrome extension.

## Overview

The dynamic usage enforcement system provides real-time, hard limits for free tier users by:
1. **Counting actual items** in storage rather than relying on cached counters
2. **Blocking creation** when limits are reached
3. **Showing clear messaging** about what action is needed
4. **Providing upgrade prompts** alongside deletion suggestions

## Free Tier Limits

- **Notes**: Maximum 10 notes
- **Highlights**: Maximum 10 highlights  
- **Notebooks**: Maximum 2 notebooks

## Key Features

### 1. Real-Time Counting
The system counts actual items in Chrome storage:
- Notes: Counts both regular notes (`stashy_note_*`) and global notes (`stashy_global_note_*`)
- Highlights: Counts all highlights across all pages (`stashy_highlights_*`)
- Notebooks: Counts notebooks in the `stashy_notebooks` storage

### 2. Dynamic Enforcement
When a free user tries to create a new item:
- System checks current count vs. limit
- If at limit, blocks creation and shows enforcement message
- Message includes current count and suggests deletion or upgrade

### 3. Enhanced User Experience
- Clear messaging: "You have 10/10 notes. Delete some notes to create new ones."
- Upgrade prompts alongside enforcement messages
- Visual indicators (warning colors, at-limit styling)
- Consistent enforcement across all creation points

## Implementation Details

### Core Functions

#### `countActualItems(type)`
Counts real items in storage for dynamic enforcement:
```javascript
const noteCount = await window.StashyPremium.countActualItems('notes');
const notebookCount = await window.StashyPremium.countActualItems('notebooks');
const highlightCount = await window.StashyPremium.countActualItems('highlights');
```

#### `canCreateItem(type)`
Checks if user can create a new item with dynamic enforcement:
```javascript
const checkResult = await window.StashyPremium.canCreateItem('notebook');
if (!checkResult.canCreate) {
    // Show enforcement message and block creation
    window.StashyPremium.showUsageEnforcementMessage(checkResult, 'notebook-creation');
    return false;
}
```

#### `showUsageEnforcementMessage(checkResult, context)`
Shows dynamic usage enforcement message with upgrade options.

### Integration Points

#### Dashboard
- **Notebook Creation**: Checks limits before showing name prompt
- **Usage Counters**: Updates with real-time counts
- **Enforcement Banners**: Shows temporary banners when limits reached

#### Content Scripts
- **Note Creation**: Checks limits before saving new notes
- **Highlight Creation**: Checks limits before creating highlights
- **Dynamic Messaging**: Shows enforcement messages in page context

#### Popup
- **Usage Display**: Shows real-time counts with warning indicators
- **Visual Feedback**: Color-coded counters (normal, warning, at-limit)

## User Flow Examples

### Scenario 1: Free User at Note Limit
1. User has 10/10 notes and tries to create a new note
2. System blocks creation and shows message:
   "You have 10/10 notes. Delete some notes to create new ones, or upgrade to Stashy Pro for unlimited notes."
3. User can either delete existing notes or upgrade to Pro

### Scenario 2: Free User at Notebook Limit
1. User has 2/2 notebooks and clicks "Add Notebook"
2. System blocks the name prompt and shows enforcement message
3. Dashboard shows upgrade banner with clear action options

### Scenario 3: Free User at Highlight Limit
1. User has 10/10 highlights and tries to highlight text
2. System clears selection and shows enforcement message
3. Content script provides upgrade prompt and deletion suggestion

## Technical Benefits

### 1. Accurate Enforcement
- No reliance on potentially outdated cached counters
- Real-time validation against actual storage contents
- Prevents circumvention through cache manipulation

### 2. Consistent Experience
- Same enforcement logic across all creation points
- Unified messaging and upgrade prompts
- Consistent visual indicators

### 3. Performance Optimized
- Efficient storage queries with minimal overhead
- Cached results where appropriate
- Background updates every 30 seconds

## Testing the System

### Manual Testing
1. Create items up to the free tier limits
2. Try to create additional items
3. Verify enforcement messages appear
4. Test deletion and re-creation flow
5. Verify upgrade prompts work correctly

### Developer Testing
Use the premium manager testing utilities:
```javascript
// Reset usage counters for testing
window.StashyPremium.testing.resetUsageCounters();

// Check current counts
const counts = await Promise.all([
    window.StashyPremium.countActualItems('notes'),
    window.StashyPremium.countActualItems('notebooks'),
    window.StashyPremium.countActualItems('highlights')
]);
console.log('Current counts:', counts);
```

## Future Enhancements

1. **Analytics Integration**: Track enforcement events for insights
2. **Smart Suggestions**: Suggest which items to delete based on usage
3. **Bulk Management**: Provide tools for managing items at limits
4. **Progressive Warnings**: Warn users as they approach limits
5. **Temporary Overrides**: Allow brief overages with grace periods

## Conclusion

The dynamic usage enforcement system provides a robust, user-friendly way to manage free tier limitations while encouraging upgrades to Pro. It ensures accurate limit enforcement while maintaining a positive user experience through clear messaging and helpful guidance.
